# Docker Network Issue Fix Tool

param()

Write-Host "Docker Network Issue Fix Tool" -ForegroundColor Green
Write-Host "=============================" -ForegroundColor Green

# 1. Restart Docker service
Write-Host ""
Write-Host "1. Restarting Docker service..." -ForegroundColor Yellow
try {
    & net stop com.docker.service 2>$null
    Start-Sleep -Seconds 3
    & net start com.docker.service 2>$null
    Write-Host "✓ Docker service restarted" -ForegroundColor Green
} catch {
    Write-Host "! Unable to restart Docker service, please manually restart Docker Desktop" -ForegroundColor Yellow
}

# 2. Clean Docker networks
Write-Host ""
Write-Host "2. Cleaning Docker networks..." -ForegroundColor Yellow
try {
    # Stop all containers
    $containers = & docker ps -q
    if ($containers) {
        & docker stop $containers
        Write-Host "✓ Stopped all running containers" -ForegroundColor Green
    }

    # Remove unused networks
    & docker network prune -f
    Write-Host "✓ Cleaned unused networks" -ForegroundColor Green
} catch {
    Write-Host "! Error occurred during network cleanup" -ForegroundColor Yellow
}

# 3. Reset DNS settings
Write-Host ""
Write-Host "3. Resetting DNS settings..." -ForegroundColor Yellow
try {
    & ipconfig /flushdns
    Write-Host "✓ DNS cache cleared" -ForegroundColor Green
} catch {
    Write-Host "! DNS cleanup failed" -ForegroundColor Yellow
}

# 4. Test network connectivity
Write-Host ""
Write-Host "4. Testing network connectivity..." -ForegroundColor Yellow

$testUrls = @(
    "registry-1.docker.io",
    "index.docker.io",
    "registry.docker-cn.com",
    "docker.mirrors.ustc.edu.cn"
)

foreach ($url in $testUrls) {
    try {
        $result = Test-NetConnection -ComputerName $url -Port 443 -WarningAction SilentlyContinue
        if ($result.TcpTestSucceeded) {
            Write-Host ("✓ " + $url + " connection successful") -ForegroundColor Green
        } else {
            Write-Host ("✗ " + $url + " connection failed") -ForegroundColor Red
        }
    } catch {
        Write-Host ("✗ " + $url + " connection test failed") -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "Fix completed!" -ForegroundColor Green
Write-Host ""
Write-Host "If the problem persists, please try:" -ForegroundColor Yellow
Write-Host "1. Restart computer" -ForegroundColor White
Write-Host "2. Reinstall Docker Desktop" -ForegroundColor White
Write-Host "3. Configure Docker mirror sources: .\scripts\setup-docker-mirrors.ps1" -ForegroundColor White

Read-Host "Press any key to exit"
