# Docker Image Export Tool - Offline Deployment Package Generator
param(
    [switch]$IncludeBase,
    [string]$OutputDir = "docker-images"
)

Write-Host "Docker Image Export Tool" -ForegroundColor Green
Write-Host "========================" -ForegroundColor Green

# Check Docker environment
Write-Host "Checking Docker environment..." -ForegroundColor Yellow
try {
    & docker info 2>$null | Out-Null
    if ($LASTEXITCODE -ne 0) {
        Write-Host "✗ Docker service not started" -ForegroundColor Red
        exit 1
    }
    Write-Host "✓ Docker environment OK" -ForegroundColor Green
} catch {
    Write-Host "✗ Docker not installed or service not started" -ForegroundColor Red
    exit 1
}

# Build project images
Write-Host ""
Write-Host "Building project images..." -ForegroundColor Yellow
& docker-compose build
if ($LASTEXITCODE -ne 0) {
    Write-Host "✗ Image build failed" -ForegroundColor Red
    exit 1
}
Write-Host "✓ Image build completed" -ForegroundColor Green

# Get project image list
$projectName = (Get-Item .).Name
$projectImages = & docker images --format "{{.Repository}}:{{.Tag}}" | Where-Object { $_ -match $projectName }

if (-not $projectImages) {
    Write-Host "✗ No project images found" -ForegroundColor Red
    exit 1
}

# Base image list
$baseImages = @()
if ($IncludeBase) {
    $baseImages = @("mongo:4.4", "node:16-alpine", "nginx:alpine")
    Write-Host ("Including base images: " + ($baseImages -join ", ")) -ForegroundColor Cyan
}

# Merge image lists
$allImages = $projectImages + $baseImages

# Create output directory
Write-Host ""
Write-Host "Creating output directory..." -ForegroundColor Yellow

if (Test-Path $OutputDir) {
    Remove-Item $OutputDir -Recurse -Force
}

$imagesDir = Join-Path $OutputDir "images"
$scriptsDir = Join-Path $OutputDir "scripts"
$configDir = Join-Path $OutputDir "config"

New-Item -ItemType Directory -Path $imagesDir -Force | Out-Null
New-Item -ItemType Directory -Path $scriptsDir -Force | Out-Null
New-Item -ItemType Directory -Path $configDir -Force | Out-Null

Write-Host "✓ Output directory created" -ForegroundColor Green

# Export images
Write-Host ""
Write-Host "Exporting Docker images..." -ForegroundColor Yellow

$exportedFiles = @()
$totalSize = 0

foreach ($image in $allImages) {
    $fileName = $image -replace "[:/]", "_"
    $fileName += ".tar"
    $filePath = Join-Path $imagesDir $fileName

    Write-Host ("Exporting: " + $image + " -> " + $fileName) -ForegroundColor Cyan

    try {
        & docker save -o $filePath $image
        if ($LASTEXITCODE -eq 0) {
            $fileSize = [math]::Round((Get-Item $filePath).Length / 1MB, 2)
            $totalSize += $fileSize
            $exportedFiles += @{
                Image = $image
                File = $fileName
                Size = $fileSize.ToString() + " MB"
            }
            Write-Host ("✓ Export completed: " + $fileSize + " MB") -ForegroundColor Green
        } else {
            Write-Host ("✗ Export failed: " + $image) -ForegroundColor Red
        }
    } catch {
        Write-Host ("✗ Export error: " + $image + " - " + $_.Exception.Message) -ForegroundColor Red
    }
}

Write-Host ""
Write-Host ("Image export completed, total size: " + [math]::Round($totalSize, 2) + " MB") -ForegroundColor Green

# Copy and modify configuration files
Write-Host ""
Write-Host "Copying and modifying configuration files..." -ForegroundColor Yellow

if (Test-Path "docker-compose.yml") {
    # Read the original docker-compose.yml with proper encoding
    $composeContent = Get-Content "docker-compose.yml" -Raw -Encoding UTF8

    # Replace build directives with image directives for deployment
    $projectName = (Get-Item .).Name

    # Replace api-server build directive
    $composeContent = $composeContent -replace "build: \./apiServer", "image: ${projectName}-api-server:latest"

    # Replace web-client build directive (multi-line)
    $composeContent = $composeContent -replace "build:\s*\r?\n\s*context: \./myClient\s*\r?\n\s*dockerfile: Dockerfile\.prod", "image: ${projectName}-web-client:latest"

    # Write the modified content to the config directory
    $outputPath = Join-Path $configDir "docker-compose.yml"
    try {
        [System.IO.File]::WriteAllText($outputPath, $composeContent, [System.Text.Encoding]::UTF8)
        Write-Host "✓ Generated deployment docker-compose.yml" -ForegroundColor Green
    } catch {
        Write-Host "! Error writing docker-compose.yml: $($_.Exception.Message)" -ForegroundColor Red
        # Fallback to simple copy
        Copy-Item "docker-compose.yml" $configDir
        Write-Host "✓ Copied original docker-compose.yml (fallback)" -ForegroundColor Yellow
    }
} else {
    Write-Host "! docker-compose.yml not found" -ForegroundColor Yellow
}

# Copy deployment scripts
Write-Host ""
Write-Host "Copying deployment scripts..." -ForegroundColor Yellow

$templateDir = "scripts-template"
if (Test-Path $templateDir) {
    # Copy all script templates
    $scriptFiles = Get-ChildItem $templateDir -File
    foreach ($script in $scriptFiles) {
        Copy-Item $script.FullName $scriptsDir
        Write-Host ("✓ Copied " + $script.Name) -ForegroundColor Green
    }
} else {
    Write-Host "! Script template directory not found, generating basic scripts" -ForegroundColor Yellow
    
    # If no template directory, copy existing scripts
    $existingScripts = @("scripts/start.ps1", "scripts/stop.ps1", "scripts/connect-mongodb.ps1", "scripts/deploy.ps1")
    foreach ($script in $existingScripts) {
        if (Test-Path $script) {
            Copy-Item $script $scriptsDir
            Write-Host ("✓ Copied " + (Split-Path $script -Leaf)) -ForegroundColor Green
        }
    }
}

# Generate image list
$imageList = @"
# Image List

## Included Image Files

| Image Name | File Name | Size |
|------------|-----------|------|
"@

foreach ($file in $exportedFiles) {
    $imageList += "`n| " + $file.Image + " | " + $file.File + " | " + $file.Size + " |"
}

$imageList += "`n`n**Total Size**: " + [math]::Round($totalSize, 2) + " MB"
$imageList += "`n**File Count**: " + $exportedFiles.Count + " files"

$imageList | Out-File -FilePath (Join-Path $OutputDir "IMAGES.md") -Encoding UTF8

Write-Host "✓ Documentation generated successfully" -ForegroundColor Green

# Show completion information
Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "Docker image export completed!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host ("Package location: " + (Resolve-Path $OutputDir).Path) -ForegroundColor Cyan
Write-Host ""
Write-Host "Contents:" -ForegroundColor Yellow
Write-Host ("- Image files: " + $exportedFiles.Count + " files") -ForegroundColor White
Write-Host ("- Total size: " + [math]::Round($totalSize, 2) + " MB") -ForegroundColor White
Write-Host "- Cross-platform scripts: Windows + Linux/macOS" -ForegroundColor White
Write-Host "- Complete documentation: README.md + IMAGES.md" -ForegroundColor White
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Copy 'docker-images' directory to target server" -ForegroundColor White
Write-Host "2. Run the corresponding deployment script on target server" -ForegroundColor White
Write-Host "   - Windows: .\scripts\deploy.ps1" -ForegroundColor White
Write-Host "   - Linux/macOS: ./scripts/deploy.sh" -ForegroundColor White
Write-Host ""
