#!/bin/bash

# MongoDB Connection Tool (Linux/macOS)
echo "GCC-XKD MongoDB Connection Tool"
echo "==============================="

# Check Docker service status
check_mongodb_status() {
    if docker-compose ps | grep -q "mongodb.*Up"; then
        echo "✓ MongoDB container is running"
        return 0
    else
        echo "✗ MongoDB container not running"
        echo "Please start services first: docker-compose up -d"
        return 1
    fi
}

# Connection options
show_connection_options() {
    echo ""
    echo "Connection options:"
    echo "1. Enter MongoDB Shell"
    echo "2. View database status"
    echo "3. Show connection information"
    echo "4. Exit"
    echo ""
}

# Enter MongoDB Shell
enter_mongo_shell() {
    echo "Entering MongoDB Shell..."
    docker exec -it xkd-mongodb mongo
}

# View database status
show_db_status() {
    echo "Database status:"
    docker exec xkd-mongodb mongo --eval "db.adminCommand('serverStatus')" --quiet
}

# Show connection information
show_connection_info() {
    echo "MongoDB connection information:"
    echo "- Internal address: mongodb://localhost:27017"
    echo "- External access address: mongodb://localhost:27018"
    echo "- Database name: Please check application configuration"
    echo ""
    echo "Connect using external tools:"
    echo "mongodb://localhost:27018"
}

# Main process
if ! check_mongodb_status; then
    exit 1
fi

while true; do
    show_connection_options
    read -p "Please select an option (1-4): " choice

    case $choice in
        1)
            enter_mongo_shell
            ;;
        2)
            show_db_status
            ;;
        3)
            show_connection_info
            ;;
        4)
            echo "Exit"
            break
            ;;
        *)
            echo "Invalid selection, please enter 1-4"
            ;;
    esac

    echo ""
    read -p "Press any key to continue..."
    echo ""
done
