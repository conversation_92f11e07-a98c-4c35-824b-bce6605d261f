#!/bin/bash

# GCC-XKD Stop Script (Linux/macOS)
echo "Stopping GCC-XKD services..."

if [ -f "docker-compose.yml" ]; then
    docker-compose down
    if [ $? -eq 0 ]; then
        echo "✓ Services stopped"
    else
        echo "✗ Error occurred while stopping services"
    fi
else
    echo "✗ docker-compose.yml configuration file not found"
    echo "Please ensure you run this script in the correct directory"
fi
