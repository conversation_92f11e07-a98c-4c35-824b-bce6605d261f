# GCC-XKD MongoDB Connection Tool (Windows)

param(
    [string]$Action = "connect",
    [string]$Database = "gcc_xkd"
)

Write-Host "GCC-XKD MongoDB Management Tool" -ForegroundColor Green
Write-Host "===============================" -ForegroundColor Green

# Check Docker service status
function Test-MongoDBService {
    Write-Host "Checking MongoDB service status..." -ForegroundColor Yellow

    try {
        $containers = & docker-compose ps --services --filter "status=running" 2>$null
        if ($containers -contains "mongodb") {
            Write-Host "✓ MongoDB service is running" -ForegroundColor Green
            return $true
        } else {
            Write-Host "✗ MongoDB service not running" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "✗ Unable to check service status" -ForegroundColor Red
        return $false
    }
}

# Connect to MongoDB
function Connect-MongoDB {
    Write-Host "Connecting to MongoDB..." -ForegroundColor Yellow
    Write-Host "Database: $Database" -ForegroundColor Cyan
    Write-Host "Port: 27018" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Starting MongoDB Shell..." -ForegroundColor Yellow

    & docker-compose exec mongodb mongo $Database
}

# Show database information
function Show-DatabaseInfo {
    Write-Host "Getting database information..." -ForegroundColor Yellow

    $mongoCmd = "db.stats(); db.runCommand('listCollections').cursor.firstBatch.forEach(function(collection) { print('Collection: ' + collection.name); });"
    & docker-compose exec mongodb mongo $Database --eval $mongoCmd
}

# Backup database
function Backup-Database {
    $backupDir = "mongodb-backup"
    $timestamp = Get-Date -Format "yyyyMMdd-HHmmss"
    $backupPath = "$backupDir/$Database-$timestamp"

    Write-Host "Backing up database to: $backupPath" -ForegroundColor Yellow

    if (-not (Test-Path $backupDir)) {
        New-Item -ItemType Directory -Path $backupDir -Force | Out-Null
    }

    & docker-compose exec mongodb mongodump --db $Database --out /tmp/backup
    & docker cp (& docker-compose ps -q mongodb):/tmp/backup/$Database $backupPath

    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Backup completed: $backupPath" -ForegroundColor Green
    } else {
        Write-Host "✗ Backup failed" -ForegroundColor Red
    }
}

# Main program
Write-Host ""

if (-not (Test-MongoDBService)) {
    Write-Host ""
    Write-Host "Please start MongoDB service first:" -ForegroundColor Yellow
    Write-Host "docker-compose up -d mongodb" -ForegroundColor White
    Write-Host ""
    Read-Host "Press any key to exit"
    exit 1
}

Write-Host ""

switch ($Action.ToLower()) {
    "connect" {
        Connect-MongoDB
    }
    "info" {
        Show-DatabaseInfo
    }
    "backup" {
        Backup-Database
    }
    default {
        Write-Host "Available actions:" -ForegroundColor Yellow
        Write-Host "- connect: Connect to MongoDB Shell" -ForegroundColor White
        Write-Host "- info: Show database information" -ForegroundColor White
        Write-Host "- backup: Backup database" -ForegroundColor White
        Write-Host ""
        Write-Host "Usage examples:" -ForegroundColor Cyan
        Write-Host ".\connect-mongodb.ps1 -Action connect" -ForegroundColor White
        Write-Host ".\connect-mongodb.ps1 -Action info" -ForegroundColor White
        Write-Host ".\connect-mongodb.ps1 -Action backup" -ForegroundColor White
    }
}

Write-Host ""
Read-Host "Press any key to exit"
