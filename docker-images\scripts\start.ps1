# GCC-XKD Startup Script
param()

Write-Host "Starting GCC-XKD services..." -ForegroundColor Green

# Check if Docker is installed
function Test-DockerInstalled {
    try {
        $dockerVersion = & docker --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ Docker installed: $dockerVersion" -ForegroundColor Green
            return $true
        } else {
            Write-Host "✗ Docker not installed" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "✗ Docker not installed" -ForegroundColor Red
        return $false
    }
}

# Check Docker service status
function Test-DockerRunning {
    try {
        & docker info 2>$null | Out-Null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ Docker service is running" -ForegroundColor Green
            return $true
        } else {
            Write-Host "✗ Docker service not started" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "✗ Docker service not started" -ForegroundColor Red
        return $false
    }
}

# Check docker-compose.yml file
function Test-ComposeFile {
    if (Test-Path "docker-compose.yml") {
        Write-Host "✓ Found docker-compose.yml configuration file" -ForegroundColor Green
        return $true
    } else {
        Write-Host "✗ docker-compose.yml configuration file not found" -ForegroundColor Red
        Write-Host "Please ensure you run this script in the project root directory" -ForegroundColor Yellow
        return $false
    }
}

# Start services
function Start-Services {
    Write-Host "Starting Docker services..." -ForegroundColor Yellow

    & docker-compose up -d

    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Services started successfully" -ForegroundColor Green

        # Wait for services to be ready
        Write-Host "Waiting for services to initialize..." -ForegroundColor Yellow
        Start-Sleep -Seconds 5

        # Show service status
        Write-Host ""
        Write-Host "Service status:" -ForegroundColor Cyan
        & docker-compose ps

        Write-Host ""
        Write-Host "Access URLs:" -ForegroundColor Cyan
        Write-Host "- Frontend: http://localhost" -ForegroundColor White
        Write-Host "- API: http://localhost:3000" -ForegroundColor White
        Write-Host "- MongoDB: localhost:27018" -ForegroundColor White

        Write-Host ""
        Write-Host "Common commands:" -ForegroundColor Yellow
        Write-Host "- View logs: docker-compose logs -f" -ForegroundColor White
        Write-Host "- Stop services: docker-compose down" -ForegroundColor White
        Write-Host "- Restart services: docker-compose restart" -ForegroundColor White

        return $true
    } else {
        Write-Host "✗ Failed to start services" -ForegroundColor Red
        Write-Host ""
        Write-Host "Please check error messages and try the following:" -ForegroundColor Yellow
        Write-Host "1. Check if ports are in use" -ForegroundColor White
        Write-Host "2. View detailed logs: docker-compose logs" -ForegroundColor White
        Write-Host "3. Rebuild images: docker-compose build" -ForegroundColor White
        return $false
    }
}

# Main program
Write-Host ""

# Check environment
if (-not (Test-DockerInstalled)) {
    Write-Host ""
    Write-Host "Please install Docker Desktop first" -ForegroundColor Red
    Read-Host "Press any key to exit"
    exit 1
}

if (-not (Test-DockerRunning)) {
    Write-Host ""
    Write-Host "Please start Docker service first" -ForegroundColor Red
    Read-Host "Press any key to exit"
    exit 1
}

if (-not (Test-ComposeFile)) {
    Read-Host "Press any key to exit"
    exit 1
}

Write-Host ""

# Start services
if (-not (Start-Services)) {
    Read-Host "Press any key to exit"
    exit 1
}

Write-Host ""
Read-Host "Press any key to exit"
