# GCC-XKD Startup Script (Windows)

Write-Host "Starting GCC-XKD services..." -ForegroundColor Green

# Check configuration file
if (-not (Test-Path "docker-compose.yml")) {
    if (Test-Path "config/docker-compose.yml") {
        Write-Host "Copying configuration file..." -ForegroundColor Yellow
        Copy-Item "config/docker-compose.yml" . -Force
    } else {
        Write-Host "✗ docker-compose.yml configuration file not found" -ForegroundColor Red
        Read-Host "Press any key to exit"
        exit 1
    }
}

# Start services
Write-Host "Starting Docker services..." -ForegroundColor Yellow
& docker-compose up -d

if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ Services started successfully" -ForegroundColor Green
    Write-Host ""
    Write-Host "Access URLs:" -ForegroundColor Cyan
    Write-Host "- Frontend: http://localhost" -ForegroundColor White
    Write-Host "- API: http://localhost:3000" -ForegroundColor White
    Write-Host "- MongoDB: localhost:27018" -ForegroundColor White
    Write-Host ""
    Write-Host "Check status: docker-compose ps" -ForegroundColor White
    Write-Host "View logs: docker-compose logs -f" -ForegroundColor White
} else {
    Write-Host "✗ Failed to start services" -ForegroundColor Red
    Write-Host "Please check logs: docker-compose logs" -ForegroundColor Yellow
}

Read-Host "Press any key to exit"
