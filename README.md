﻿# 高层次人才服务管理平台 (gcc-xkd)

## 项目简介

西安科技大学高层次人才服务管理平台，用于管理和服务高层次人才的综合性Web应用系统。

## 项目结构

```
gcc-xkd/
├── apiServer/          # 后台API服务 (Node.js + Restify + MongoDB)
├── myClient/           # 前端Web应用 (React + Ant Design Pro)
├── docker-compose.yml  # Docker容器编排配置
├── start.*             # 启动脚本（多平台支持）
└── stop.*              # 停止脚本（多平台支持）
```

## 脚本说明

| 脚本文件 | 平台 | 功能说明 | 使用场景 |
|---------|------|----------|----------|
| `scripts/start.ps1` | Windows PowerShell | 一键启动所有Docker服务，包含环境检查 | **推荐** Windows用户使用 |
| `scripts/start.bat` | Windows 命令提示符 | 启动Docker服务的批处理版本 | PowerShell不可用时的备选方案 |
| `scripts/start.sh` | Linux/macOS | 启动Docker服务的Shell脚本 | Linux/macOS用户使用 |
| `scripts/stop.ps1` | Windows PowerShell | 停止所有Docker服务 | 配合start.ps1使用 |
| `scripts/stop.bat` | Windows 命令提示符 | 停止Docker服务的批处理版本 | 配合start.bat使用 |
| `scripts/stop.sh` | Linux/macOS | 停止Docker服务的Shell脚本 | 配合start.sh使用 |
| `scripts/connect-mongodb.ps1` | Windows PowerShell | 连接MongoDB数据库工具 | 开发调试时直接操作数据库 |
| `scripts/setup-docker-mirrors.ps1` | Windows PowerShell | 配置Docker国内镜像源 | 解决Docker镜像拉取慢的问题 |
| `scripts/fix-docker-network.ps1` | Windows PowerShell | 修复Docker网络问题 | Docker启动失败时的故障排除 |
| `export-images.ps1` | Windows PowerShell | 导出Docker镜像 | 生成完整的离线部署包 |
| `scripts/deploy.ps1` | Windows PowerShell | 一键部署脚本 | 目标服务器上的自动化部署 |

> **📝 编码说明：** PowerShell脚本使用UTF-8编码便于编辑，运行时自动设置GBK控制台输出确保中文正确显示

## 技术栈

### 后台服务 (apiServer)
- **Node.js** - 运行环境
- **Restify** - REST API框架  
- **MongoDB** - 数据库
- **PM2** - 进程管理

### 前端应用 (myClient)
- **React 16.7.0** - 前端框架
- **Ant Design Pro** - UI组件库
- **Dva** - 状态管理
- **UmiJS** - 构建工具

## 快速开始

### 使用Docker部署（推荐）

1. **确保已安装Docker和Docker Compose**

2. **一键启动所有服务**
   
   **Windows (PowerShell - 推荐):**
   ```powershell
   .\scripts\start.ps1
   ```
   
   **Windows (命令提示符):**
   ```cmd
   scripts\start.bat
   ```
   
   **Linux/macOS:**
   ```bash
   chmod +x scripts/start.sh
   ./scripts/start.sh
   ```
   
   **通用方式:**
   ```bash
   docker-compose up --build -d
   ```

3. **停止服务**
   
   **Windows (PowerShell):**
   ```powershell
   .\scripts\stop.ps1
   ```

   **Windows (命令提示符):**
   ```cmd
   scripts\stop.bat
   ```
   
   **Linux/macOS:**
   ```bash
   ./scripts/stop.sh
   ```

4. **访问应用**
   - 前端应用：http://localhost
   - 后台API：http://localhost:3000
   - MongoDB：localhost:27018 (避免与本地MongoDB冲突)

5. **常用Docker命令**
   ```bash
   # 查看服务状态
   docker-compose ps
   
   # 查看日志
   docker-compose logs -f
   
   # 重新构建并启动
   docker-compose up --build -d
   
   # 清理所有容器和数据
   docker-compose down --volumes --rmi all
   ```

## 离线部署

### 生成镜像包

**完整部署包（包含基础镜像）：**
```powershell
# 导出所有镜像，适合完全离线环境
.\export-images.ps1 -IncludeBase
```

**轻量部署包（仅项目镜像）：**
```powershell
# 仅导出项目镜像，目标服务器需要网络连接下载基础镜像
.\export-images.ps1
```

### 部署到其他服务器

#### 方法1：一键部署（推荐）
1. **传输部署包目录**到目标服务器
2. **运行一键部署脚本**：
   ```powershell
   # 进入部署包目录
   cd docker-images

   # 一键部署 - 自动检测环境、导入镜像、启动服务
   .\scripts\deploy.ps1
   ```

#### 方法2：手动部署
1. **传输部署包目录**到目标服务器
2. **进入部署包目录**：
   ```bash
   cd docker-images
   ```
3. **导入镜像**：
   ```bash
   # Linux/macOS
   ./scripts/start.sh

   # Windows
   .\scripts\start.ps1
   ```
4. **或者分步执行**：
   ```bash
   # 导入所有镜像文件
   for file in images/*.tar; do docker load -i "$file"; done

   # 复制配置文件并启动服务
   cp config/docker-compose.yml .
   docker-compose up -d
   ```

#### 部署包结构
```
docker-images/
├── images/                    # Docker 镜像文件
├── config/                    # 配置文件
│   └── docker-compose.yml
├── scripts/                   # 部署和管理脚本
│   ├── deploy.ps1            # 一键部署脚本(Windows)
│   ├── deploy.sh             # 一键部署脚本(Linux/macOS)
│   ├── start.ps1/.sh         # 启动服务脚本
│   ├── stop.ps1/.sh          # 停止服务脚本
│   └── connect-mongodb.ps1/.sh # MongoDB连接工具
├── README.md                  # 部署说明
└── IMAGES.md                  # 镜像清单
```

更多部署选项和故障排除信息请查看生成的部署包中的 README.md 文件。

### 本地开发环境

#### 后台服务启动
```bash
cd apiServer
npm install
npm start
```

#### 前端应用启动
```bash
cd myClient
npm install --legacy-peer-deps
npm start
```

## 主要功能

- **人才信息管理** - 高层次人才基本信息维护
- **目标管理** - 待遇协议、论文目标、项目目标设定
- **成果管理** - 待遇兑现、论文成果、项目成果记录
- **数据统计** - 多维度数据分析和可视化
- **汇总导出** - Excel格式报表导出
- **多语言支持** - 中文简体、中文繁体、英文

## 环境要求

### Docker部署
- **Docker**: >= 20.x
- **Docker Compose**: >= 1.29.x
- **操作系统**: Windows 10/11, macOS, Linux

### Windows用户说明
- **推荐**: 使用PowerShell脚本（`scripts/start.ps1`、`scripts/stop.ps1`）
- **备选**: 使用批处理文件（`scripts/start.bat`、`scripts/stop.bat`）
- **注意**: 脚本会自动检查Docker环境并给出安装提示
- **权限**: 如果PowerShell执行策略限制，请以管理员身份运行：
  ```powershell
  Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
  ```

#### PowerShell执行策略问题解决

如果运行PowerShell脚本时遇到执行策略错误，请使用以下方法之一：

**方法1：设置执行策略（推荐）**
1. 以管理员身份打开PowerShell
2. 运行以下命令：
   ```powershell
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
   ```
3. 输入 `Y` 确认
4. 然后正常运行脚本：
   ```powershell
   .\scripts\start.ps1
   ```

**方法2：绕过执行策略**
```powershell
PowerShell -ExecutionPolicy Bypass -File .\scripts\start.ps1
```

**方法3：使用批处理文件（最简单）**
如果PowerShell问题较多，直接使用批处理文件：
```cmd
scripts\start.bat
```

#### 常见问题排查

**基础问题**
- **脚本无法运行**: 检查PowerShell执行策略
- **Docker相关错误**: 确保Docker Desktop已安装并正在运行
- **端口占用**: 检查80端口和3000端口是否被占用
- **权限问题**: 以管理员身份运行命令提示符或PowerShell

**网络问题**
- **Docker镜像拉取失败**: 运行 `.\setup-docker-mirrors.ps1` 配置国内镜像源
- **网络连接超时**: 运行 `.\fix-docker-network.ps1` 修复网络问题
- **MongoDB连接问题**:
  - Docker MongoDB: `localhost:27018`
  - 本地MongoDB: `localhost:27017`
  - 使用 `.\connect-mongodb.ps1` 测试连接

**数据库问题**
- **MongoDB端口冲突**: Docker MongoDB使用27018端口，避免与本地MongoDB(27017)冲突
- **数据库连接工具**: 使用 `.\connect-mongodb.ps1 connect docker` 连接Docker数据库

### 环境检查功能
启动脚本会自动检查：
- ✓ Docker是否已安装
- ✓ Docker Compose是否可用  
- ✓ Docker服务是否正在运行
- ✗ 如有问题会给出具体的安装或启动指导

### Linux/macOS用户说明
- 使用`start.sh`和`stop.sh`脚本
- 首次使用需要添加执行权限：`chmod +x *.sh`

## 开发说明

### 前端开发注意事项
- 使用 `npm install --legacy-peer-deps` 解决依赖版本冲突
- 支持多环境配置：local、dev、production
- 内置Mock数据支持

### 后台开发注意事项  
- 自动执行数据库初始化
- 支持CORS跨域配置
- 文件上传功能支持

## 部署架构

Docker部署包含以下服务：
- **MongoDB容器** - 数据存储
- **API服务容器** - 后台服务（PM2管理）
- **Web服务容器** - 前端应用（Nginx部署）

## 许可证

ISC License

## 作者

西安科技大学






