# GCC-XKD Stop Script

Write-Host "Stopping GCC-XKD services..." -ForegroundColor Green

# Check docker-compose.yml file
$configPath = Join-Path $PSScriptRoot "..\config\docker-compose.yml"
if (-not (Test-Path $configPath)) {
    Write-Host "✗ docker-compose.yml configuration file not found at $configPath" -ForegroundColor Red
    Write-Host "Please ensure the docker-images package is complete" -ForegroundColor Yellow
    Read-Host "Press any key to exit"
    exit 1
}

Write-Host "✓ Configuration file found" -ForegroundColor Green

# Show currently running services
Write-Host ""
Write-Host "Currently running services:" -ForegroundColor Yellow
& docker-compose -f $configPath ps

Write-Host ""
Write-Host "Stopping services..." -ForegroundColor Yellow

# Stop services
& docker-compose -f $configPath down

if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ Services stopped" -ForegroundColor Green

    Write-Host ""
    Write-Host "For complete cleanup, you can run the following commands:" -ForegroundColor Cyan
    Write-Host "- Remove volumes: docker-compose -f .\config\docker-compose.yml down --volumes" -ForegroundColor White
    Write-Host "- Remove images: docker-compose -f .\config\docker-compose.yml down --rmi all" -ForegroundColor White
    Write-Host "- Complete cleanup: docker-compose -f .\config\docker-compose.yml down --volumes --rmi all" -ForegroundColor White
} else {
    Write-Host "✗ Error occurred while stopping services" -ForegroundColor Red
    Write-Host ""
    Write-Host "You can try force stop:" -ForegroundColor Yellow
    Write-Host "docker-compose -f .\config\docker-compose.yml kill" -ForegroundColor White
}

Write-Host ""
Read-Host "Press any key to exit"
