# GCC-XKD Stop Script

Write-Host "Stopping GCC-XKD services..." -ForegroundColor Green

# Check docker-compose.yml file
if (-not (Test-Path "docker-compose.yml")) {
    Write-Host "✗ docker-compose.yml configuration file not found" -ForegroundColor Red
    Write-Host "Please ensure you run this script in the project root directory" -ForegroundColor Yellow
    Read-Host "Press any key to exit"
    exit 1
}

Write-Host "✓ Configuration file found" -ForegroundColor Green

# Show currently running services
Write-Host ""
Write-Host "Currently running services:" -ForegroundColor Yellow
& docker-compose ps

Write-Host ""
Write-Host "Stopping services..." -ForegroundColor Yellow

# Stop services
& docker-compose down

if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ Services stopped" -ForegroundColor Green

    Write-Host ""
    Write-Host "For complete cleanup, you can run the following commands:" -ForegroundColor Cyan
    Write-Host "- Remove volumes: docker-compose down --volumes" -ForegroundColor White
    Write-Host "- Remove images: docker-compose down --rmi all" -ForegroundColor White
    Write-Host "- Complete cleanup: docker-compose down --volumes --rmi all" -ForegroundColor White
} else {
    Write-Host "✗ Error occurred while stopping services" -ForegroundColor Red
    Write-Host ""
    Write-Host "You can try force stop:" -ForegroundColor Yellow
    Write-Host "docker-compose kill" -ForegroundColor White
}

Write-Host ""
Read-Host "Press any key to exit"
